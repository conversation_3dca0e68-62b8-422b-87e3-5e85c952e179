/* Custom styles for the application */

/* Table with borders and vertical dividers */
.table-with-dividers {
  border-collapse: collapse;
  width: 100%;
}

.table-with-dividers th,
.table-with-dividers td {
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
}

/* Trace component styles */
.table-cell-truncate {
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.payload-preview-truncate {
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Remove borders from input fields inside table cells */
.table-with-dividers input {
  border: none;
  background: transparent;
}

/* Remove focus outline from table input fields */
.table-with-dividers input:focus {
  outline: none;
  box-shadow: none;
  border: none;
}

/* Larger font size for variables */
.variable-text {
  font-size: 1.1rem;
}

.table-with-dividers input {
  font-size: 1.1rem;
}



/* Content area - now full width since icon menu is inside unified panel */
.content-area {
  margin-left: 0; /* No margin needed since icon menu is inside panel */
  transition: margin-left 0.3s ease-in-out;
  padding-bottom: 2.5rem; /* Add padding for bottom bar */
  height: calc(100vh - 2.5rem); /* Full height minus bottom bar */
  padding-left: 0px; /* Add padding for bottom bar */
}

/* Prevent text selection during resize */
.resize-active {
  user-select: none;
  cursor: ew-resize;
}

/* Bottom bar styles - now full width since icon menu is inside unified panel */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0; /* Start from left edge since icon menu is inside panel */
  right: 0;
  height: 2.5rem;
  background-color: transparent;
  border-top: 1px solid var(--color-base-300);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
  z-index: 40;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.bottom-bar-left {
  display: flex;
  align-items: center;
}

.bottom-bar-center {
  flex-grow: 1;
  text-align: center;
  color: var(--color-base-content);
  opacity: 0.7;
  font-size: 0.875rem;
}

.bottom-bar-right {
  display: flex;
  align-items: center;
}



/* Content area margin is now always 3rem since connection menu is not used */

/* Unified Panel Styles - now contains icon menu and all components */
.unified-panel {
  background-color: var(--color-base-100);
  /* No borders or shadows for full-screen layout */
  /* Full width and height with no gaps */
  margin: 0;
  padding: 0;
  width: 100vw;
  height: calc(100vh - 2.5rem); /* Full height minus bottom bar only */
  overflow: visible; /* Allow scrolling */
}

/* Panel Icon Menu Styles */
.panel-icon-menu {
  min-height: 100%;
  background-color: var(--color-base-200) !important;
}

/* Main content area scrolling */
.unified-panel > div:last-child {
  overflow-y: auto;
  height: 100%;
  flex: 1;
  min-height: 0; /* Important for flex children to shrink */
}

/* Ensure trace component can grow and scroll properly */
.main-content-area {
  height: 100%;
  min-height: 0;
}

/* Ensure all components within the panel have consistent background */
.unified-panel .broker-tabs-container,
.unified-panel .bg-base-100,
.unified-panel .trace-section {
  background-color: var(--color-base-100) !important;
}

/* Remove any conflicting borders and shadows from child components */
.unified-panel .card,
.unified-panel .trace-section,
.unified-panel .collapse {
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* Ensure consistent spacing between sections */
.unified-panel > div + div {
  border-top: 1px solid var(--color-base-300);
}

/* Remove any extra margins from components inside the panel */
.unified-panel .mb-2,
.unified-panel .mt-1 {
  margin: 0 !important;
}

/* Bottom bar position updated to align with content area */



/* Position the collapse component to allow absolute positioning of buttons */
.collapse {
  position: relative;
}

/* Ensure the collapse arrow is visible */
.collapse-arrow .collapse-title:after {
  z-index: 20;
}

/* Styles for collapse content with AutoAnimate */
.collapse-content {
  overflow: hidden;
}

/* Override DaisyUI's default collapse behavior for AutoAnimate compatibility */
.collapse:not(details) .collapse-content {
  padding-top: 0;
  padding-bottom: 0;
  overflow: hidden;
}

.collapse:not(details) input[type="checkbox"]:checked ~ .collapse-content {
  max-height: none; /* Let AutoAnimate handle the height */
  padding-top: 1rem;
  padding-bottom: 1rem;
  opacity: 1;
}

.collapse:not(details) input[type="checkbox"]:not(:checked) ~ .collapse-content {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  opacity: 0;
}



.invisible {
  visibility: hidden;
}

.visible {
  visibility: visible;
}

.group-hover\:visible:not(.visible) {
  visibility: hidden;
}

.group:hover .group-hover\:visible {
  visibility: visible;
}





/* Badge styles */
.badge-success {
  background-color: rgba(34, 197, 94, 0.1);
  color: rgb(34, 197, 94);
  border-color: rgb(34, 197, 94);
}

.badge-warning {
  background-color: rgba(234, 179, 8, 0.1);
  color: rgb(234, 179, 8);
  border-color: rgb(234, 179, 8);
}

.badge-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
  border-color: rgb(239, 68, 68);
}



/* Modal blur effect */
.unified-panel,
.bottom-bar {
  transition: filter 0.3s ease-out;
}

body.modal-open .unified-panel,
body.modal-open .bottom-bar {
  filter: blur(5px);
}

/* Ensure the modal content itself is not blurred */
.modal-open [aria-modal="true"],
.modal-open [aria-modal="true"] * {
  filter: none !important;
}

/* Glass effect for modal backdrop */
.glass-effect {
  background-color: var(--color-base-300) !important;
  opacity: 0.7;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

/* Modal content animation and styling */
.modal-content {
  animation: modal-appear 0.3s ease-out;
  border: 1px solid var(--color-base-300);
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Equal height connection containers */
#connection-status-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.25rem;
}

.connection-status-group {
  display: flex;
  flex-direction: column;
}

/* Broker Tabs Styles */
.broker-tabs-container {
  position: sticky;
  top: 0;
  z-index: 20;
}

/* Override broker tabs styles when inside unified panel */
.unified-panel .broker-tabs-container {
  box-shadow: none !important;
  border: none !important;
  border-radius: 0 !important;
}

.broker-tab {
  position: relative;
  user-select: none;
}

.broker-tab:hover .opacity-0 {
  opacity: 1;
}

/* Sortable styles for broker tabs */
.broker-tab.sortable-ghost {
  opacity: 0.5;
  background-color: var(--color-base-300) !important;
}

.broker-tab.sortable-chosen {
  transform: scale(1.02);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.broker-tab.sortable-drag {
  transform: rotate(2deg);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Scrollbar for tabs container */
.broker-tabs-container .flex-1::-webkit-scrollbar {
  height: 4px;
}

.broker-tabs-container .flex-1::-webkit-scrollbar-track {
  background: transparent;
}

.broker-tabs-container .flex-1::-webkit-scrollbar-thumb {
  background: var(--color-base-300);
  border-radius: 2px;
}

.broker-tabs-container .flex-1::-webkit-scrollbar-thumb:hover {
  background: var(--color-base-content);
  opacity: 0.5;
}

.equal-height-container {
  /* Only show scrollbar when needed */
  overflow-y: visible;
  max-height: 12rem; /* Height of approximately 6 connection items */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Webkit scrollbar styles for connection containers */
.equal-height-container::-webkit-scrollbar {
  width: 4px;
}

.equal-height-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.equal-height-container::-webkit-scrollbar-track {
  background: transparent;
}

/* Custom focus styles for input fields */
input:focus:not([type="checkbox"]):not([type="radio"]),
select:focus,
textarea:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: var(--color-warning) !important;
  border-width: 1px !important;
}

/* Style for label.input that contains an input field */
label.input.input-bordered:focus-within {
  outline: none !important;
  box-shadow: none !important;
  border-color: var(--color-warning) !important;
  border-width: 1px !important;
}

/* Remove default focus styles for inputs inside label.input */
label.input.input-bordered input:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Preserve original styles for variable table inputs */
.table-with-dividers input:focus {
  border-color: transparent !important;
  border-width: 0 !important;
}

/* Connection table animation styles */
.connection-table-container {
  position: relative;
  overflow: hidden;
}

.connection-table-container tbody {
  position: relative;
}

.connection-table-container tr {
  position: relative;
  transition: background-color 0.2s ease;
}

/* Animation support for row removal */
.connection-table-container tr.removing {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 10;
  pointer-events: none;
}

/* Smooth transitions for status changes */
.connection-table-container .swap {
  transition: all 0.2s ease;
}

.connection-table-container .swap:hover {
  transform: scale(1.05);
}

/* Highlight effect for status changes */
.connection-table-container tr.status-changing {
  background-color: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
}

/* Ensure table maintains layout during animations */
.connection-table-container table {
  table-layout: fixed;
  width: 100%;
}

/* Prevent layout shifts during animations */
.connection-table-container tbody tr td {
  vertical-align: top;
}


